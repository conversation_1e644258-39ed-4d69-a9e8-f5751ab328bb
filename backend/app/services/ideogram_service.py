"""
Ideogram.ai Service - Specialized service for generating advertisements using Ideogram.ai
Perfect for text-heavy marketing materials with professional quality at low cost.
"""

import logging
import httpx
import os
from typing import Dict, Any
from fastapi import UploadFile
from app.core.config import settings

logger = logging.getLogger(__name__)


class IdeogramService:
    """Service for generating advertisements using Ideogram.ai - optimized for text and marketing."""
    
    def __init__(self):
        """Initialize the Ideogram service."""
        self.api_key = os.getenv("IDEOGRAM_API_KEY") or settings.IDEOGRAM_API_KEY
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_ad(self, prompt: str, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate an advertisement using Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for Ideogram 3.0 Quality API (excellent for text)
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best text rendering
                "magic_prompt": (None, "AUTO"),  # Let Ideogram enhance prompts automatically
                "style_type": (None, "DESIGN"),  # Use DESIGN style for better text integration
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, distorted text, unreadable text, poor typography, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, watermark, low resolution"),
                "num_images": (None, "1")  # Single image for individual calls
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating advertisement with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()

                # Ideogram 3.0 response format
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_multiple_ads(self, prompt: str, num_images: int = 6, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate multiple advertisements in a single API call using Ideogram.ai.
        Much more efficient than multiple separate calls.

        Args:
            prompt: Description of the advertisement to create
            num_images: Number of images to generate (1-8, default 6)
            size: Image size

        Returns:
            Dict with success status, image URLs list, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        # Limit to API maximum
        num_images = min(max(num_images, 1), 8)

        try:
            # Use the prompt directly as Ideogram recommends - don't over-complicate
            enhanced_prompt = prompt

            # Map common sizes to Ideogram format
            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data for multiple images
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),  # Use QUALITY for best results
                "magic_prompt": (None, "AUTO"),  # Let Ideogram create variations
                "style_type": (None, "GENERAL"),  # General style for versatility
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry text, illegible text, low quality, amateur, unprofessional"),
                "num_images": (None, str(num_images))  # Generate multiple images
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating {num_images} advertisements with Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process multiple images from response
                if "data" in result and len(result["data"]) > 0:
                    images = []
                    for i, image_data in enumerate(result["data"]):
                        image_url = image_data.get("url")
                        if image_url:
                            images.append({
                                "image_url": image_url,
                                "revised_prompt": image_data.get("prompt"),
                                "metadata": {
                                    "model": "ideogram-3.0-quality",
                                    "size": size,
                                    "resolution": ideogram_size,
                                    "original_prompt": prompt,
                                    "enhanced_prompt": enhanced_prompt,
                                    "seed": image_data.get("seed"),
                                    "variation": i + 1,
                                    "is_image_safe": image_data.get("is_image_safe", True),
                                    "style_type": image_data.get("style_type", "GENERAL")
                                }
                            })

                    if images:
                        return {
                            "success": True,
                            "images": images,
                            "total_generated": len(images),
                            "total_requested": num_images
                        }
                    else:
                        return {"success": False, "error": "No valid images in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating multiple advertisements with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def generate_with_reference(self, prompt: str, reference_image: UploadFile, size: str = "1024x1024") -> Dict[str, Any]:
        """
        Generate advertisement using a reference image with Ideogram.ai.
        
        Args:
            prompt: Description of the advertisement to create
            reference_image: Reference image to guide the generation
            size: Image size
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}
        
        try:
            # Read reference image content
            image_content = await reference_image.read()
            
            # Enhanced prompt for reference-based generation
            enhanced_prompt = f"""Create a professional advertisement inspired by the reference image style with this concept: {prompt}

STYLE ADAPTATION:
- Adapt the visual style, composition, and aesthetic from the reference image
- Maintain the professional quality and commercial appeal
- Integrate text elements naturally as shown in reference
- Use similar lighting, color palette, and mood
- Keep the same level of sophistication and brand appeal

TEXT REQUIREMENTS:
- Ensure all text is clearly readable and professionally integrated
- Match the typography style and placement approach from reference
- Maintain proper text hierarchy and contrast

The result should feel cohesive with the reference while being unique and professional."""

            size_mapping = {
                "1024x1024": "1024x1024",
                "1024x1792": "1024x1792",
                "1792x1024": "1792x1024",
                "auto": "1024x1024"
            }

            ideogram_size = size_mapping.get(size, "1024x1024")

            # Prepare multipart form data with reference image for Ideogram 3.0 API
            files = {
                "prompt": (None, enhanced_prompt),
                "resolution": (None, ideogram_size),
                "rendering_speed": (None, "QUALITY"),
                "magic_prompt": (None, "AUTO"),
                "style_type": (None, "GENERAL"),
                "negative_prompt": (None, "photorealistic humans, realistic people, photography of people, realistic portraits, realistic faces, human photography, portrait photography, realistic human figures, photographic style people, realistic man, realistic woman, lifelike humans, real person photos, selfie, crowd photography, blurry, low quality, pixelated, amateur, unprofessional, poor lighting, bad composition, illegible text, low resolution"),
                "num_images": (None, "1"),
                "style_reference_images": ("reference.png", image_content, "image/png")
            }

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🖼️ Generating with reference using Ideogram.ai: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram reference error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}
                
                result = response.json()
                
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    image_url = image_data.get("url")

                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt"),
                            "metadata": {
                                "model": "ideogram-3.0-quality",
                                "size": size,
                                "resolution": ideogram_size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "seed": image_data.get("seed"),
                                "type": "reference_based",
                                "reference_used": True,
                                "is_image_safe": image_data.get("is_image_safe", True),
                                "style_type": image_data.get("style_type", "GENERAL")
                            }
                        }
                    else:
                        return {"success": False, "error": "No image URL in reference response"}
                else:
                    return {"success": False, "error": "No image data in reference response"}
                    
        except Exception as e:
            logger.error(f"Error in reference generation with Ideogram: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
ideogram_service = IdeogramService()
