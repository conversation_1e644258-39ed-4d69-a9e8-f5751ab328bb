import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Quote, TrendingUp, Award } from 'lucide-react';

interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
  agentUsed: string;
  results: {
    metric: string;
    improvement: string;
  };
}

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Marketing Manager',
    company: 'TechFlow Solutions',
    content: 'Los agentes de Emma han revolucionado nuestro soporte al cliente. El agente de IA maneja la mayoría de las consultas, liberando a nuestro equipo para enfocarse en problemas complejos.',
    rating: 5,
    avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAKfkwPNMGcrBDkFdYkqXQIfmfRzafA0OalZqn75er8pEI0idQHBDHJrpEhI8bTFQYqwCca95waICoA4kpZH-n-5x22z8ymk-XW4PljRfmR6DS2ouUn9B_wOKtAN9aHFm-pdWk8X9puJT00URBRC1lAiTkPn1eoF7_CXQSRArHWk9iaipxetNPPmzCyCLWXL7-USFZyIokUrxlFusT-TT0t9kbVbsxpThMGLPWE2-4GDmot0F9DsuZ39cvpFoKS0c50xmivUfI85Pow',
    agentUsed: 'Content Creator Pro',
    results: {
      metric: 'Tiempo de Respuesta',
      improvement: '75% más rápido'
    }
  },
  {
    id: '2',
    name: 'Ethan Wong',
    role: 'Business Analyst',
    company: 'DataDriven Corp',
    content: 'El agente de análisis de datos ha proporcionado información invaluable sobre el rendimiento de nuestro negocio, ayudándonos a identificar áreas clave de mejora.',
    rating: 5,
    avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDtTFaRgM7cGiXBAiRYDeSEy0oAxnLBQT0kcH0dXKZSY5CsY5Hb4Tb-1aYf_FIlH45yQbDVGmLLxE3IzOBOs-8EG_yAHGwAGK0Yxy3WpsuUhoILD8Au9TIjKIhWQE8pCpblvv0k_LJafVvB4jgjdPB08tQ9hVQgpoJ6EcG_9UIPhd-5PNod_znAHQn3GuGQLjkZRfKZCL0ve3Cp1k-71Omju0BxXcctANpoxkZVB-tsR2Bn2ZaTfUyL8EMJnEjeOfUxk90p39A5SKqm',
    agentUsed: 'SEOX Analyzer',
    results: {
      metric: 'Tráfico Orgánico',
      improvement: '+180% en 3 meses'
    }
  },
  {
    id: '3',
    name: 'Chloe Tan',
    role: 'Content Creator',
    company: 'Creative Studio',
    content: 'Ahora puedo crear contenido atractivo en una fracción del tiempo, gracias al agente de creación de contenido. Es un cambio total!',
    rating: 5,
    avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDSSz-lEzGaRBUbdbAKiWSrRcMss8PBMPKJUVOyYGeO0aorZw2XAMQgZ5ZYIf_aWwLgG8xrKh-y9aiXBeAb0Nkw5ZlFQhOH240g09yJdfLFP6exm6NpXdPwTl2aw6Hj6YFsoe5HlQRr88mmerea-AMBtIwx8YaXCV3hDrW0OUXH3BQes88zxWmbydKWMrBo_2JY6flUwrDQMXUXSTe6gOsadTHNmXzcXRLZNjOn1xxj_kV574kLeTxh6xsLq9mWzkBdDlSjwQ4MJJSA',
    agentUsed: 'Social Media Guru',
    results: {
      metric: 'Engagement Rate',
      improvement: '+250% promedio'
    }
  }
];

interface TestimonialsSectionProps {
  className?: string;
}

export function TestimonialsSection({ className = '' }: TestimonialsSectionProps) {
  return (
    <div className={`px-6 py-16 max-w-7xl mx-auto ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <div className="inline-flex items-center gap-2 mb-4">
          <Award className="w-6 h-6 text-[#dd3a5a]" />
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            Historias de Éxito
          </h2>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Testimonios reales de empresas que han transformado su productividad con nuestros agentes IA
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <TestimonialCard
            key={testimonial.id}
            testimonial={testimonial}
            index={index}
          />
        ))}
      </div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="text-center mt-12"
      >
        <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-2xl p-8 border border-[#3018ef]/20">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">
            ¿Listo para ser el próximo caso de éxito?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Únete a cientos de empresas que ya están transformando sus procesos con agentes IA especializados.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Badge className="bg-green-100 text-green-800 border-green-200 px-4 py-2">
              ✓ Sin configuración compleja
            </Badge>
            <Badge className="bg-blue-100 text-blue-800 border-blue-200 px-4 py-2">
              ✓ Resultados en 24 horas
            </Badge>
            <Badge className="bg-purple-100 text-purple-800 border-purple-200 px-4 py-2">
              ✓ Soporte especializado
            </Badge>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

interface TestimonialCardProps {
  testimonial: Testimonial;
  index: number;
}

function TestimonialCard({ testimonial, index }: TestimonialCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="group"
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full">
        <CardContent className="p-6">
          {/* Quote Icon */}
          <div className="flex justify-between items-start mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center">
              <Quote className="w-5 h-5 text-white" />
            </div>
            
            {/* Rating */}
            <div className="flex items-center gap-1">
              {[...Array(testimonial.rating)].map((_, i) => (
                <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
              ))}
            </div>
          </div>

          {/* Testimonial Content */}
          <blockquote className="text-gray-700 italic leading-relaxed mb-6">
            "{testimonial.content}"
          </blockquote>

          {/* Author Info */}
          <div className="flex items-center gap-4 mb-4">
            <img
              src={testimonial.avatar}
              alt={testimonial.name}
              className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
            />
            <div>
              <p className="font-bold text-gray-800">{testimonial.name}</p>
              <p className="text-sm text-gray-600">{testimonial.role}</p>
              <p className="text-xs text-gray-500">{testimonial.company}</p>
            </div>
          </div>

          {/* Agent Used */}
          <div className="mb-4">
            <Badge variant="secondary" className="bg-[#3018ef]/10 text-[#3018ef] border-[#3018ef]/20">
              Agente: {testimonial.agentUsed}
            </Badge>
          </div>

          {/* Results */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-3 border border-green-200">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-sm font-semibold text-gray-800">{testimonial.results.metric}</span>
            </div>
            <p className="text-lg font-bold text-green-600">{testimonial.results.improvement}</p>
          </div>
        </CardContent>

        {/* Hover Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg" />
      </Card>
    </motion.div>
  );
}
