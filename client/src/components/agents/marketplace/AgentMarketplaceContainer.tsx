import React from 'react';
import { AnimatePresence } from 'framer-motion';
import { Agent } from '@/data/agents-data';
import { AgentCategoryFilter } from './AgentCategoryFilter';
import { AgentSearchBar } from './AgentSearchBar';
import { AgentGrid } from './AgentGrid';
import { AgentProfileModal } from './AgentProfileModal';
import { FeaturedAgentsSection } from './FeaturedAgentsSection';
import { TestimonialsSection } from './TestimonialsSection';
import { CTASection } from './CTASection';
import { MarketplaceHero } from './MarketplaceHero';
import { useAgentFiltering } from '../hooks/useAgentFiltering';
import { useAgentModal } from '../hooks/useAgentModal';

interface AgentMarketplaceContainerProps {
  className?: string;
}

export function AgentMarketplaceContainer({ className = '' }: AgentMarketplaceContainerProps) {
  const {
    selectedCategory,
    searchQuery,
    viewMode,
    sortBy,
    filteredAgents,
    featuredAgents,
    agentCounts,
    handleCategorySelect,
    handleSearch,
    handleSortChange,
    handleViewModeChange
  } = useAgentFiltering();

  const {
    selectedAgent,
    handleAgentSelect,
    handleCloseProfile
  } = useAgentModal();

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 ${className}`}>
      {/* Hero Section */}
      <MarketplaceHero />

      {/* Featured Agents Section */}
      <FeaturedAgentsSection
        agents={featuredAgents}
        onAgentSelect={handleAgentSelect}
      />

      {/* Main Marketplace Content */}
      <div className="px-6 py-12 max-w-7xl mx-auto">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar with Categories and Filters */}
          <div className="lg:w-80 flex-shrink-0">
            <AgentCategoryFilter
              categories={categoriesData}
              selectedCategory={selectedCategory}
              onCategorySelect={handleCategorySelect}
              agentCounts={agentCounts}
            />
          </div>

          {/* Main Content Area */}
          <div className="flex-1">
            {/* Search and Controls */}
            <AgentSearchBar
              searchQuery={searchQuery}
              onSearch={handleSearch}
              sortBy={sortBy}
              onSortChange={handleSortChange}
              viewMode={viewMode}
              onViewModeChange={handleViewModeChange}
              resultCount={filteredAgents.length}
            />

            {/* Agents Grid */}
            <AgentGrid
              agents={filteredAgents}
              viewMode={viewMode}
              onAgentSelect={handleAgentSelect}
            />
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Call to Action Section */}
      <CTASection />

      {/* Agent Profile Modal */}
      <AnimatePresence>
        {selectedAgent && (
          <AgentProfileModal
            agent={selectedAgent}
            onClose={handleCloseProfile}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
