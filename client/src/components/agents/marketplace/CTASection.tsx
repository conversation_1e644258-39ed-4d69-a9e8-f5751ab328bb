import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Play, 
  CheckCircle, 
  Sparkles,
  Zap,
  Shield,
  Clock,
  Users
} from 'lucide-react';

interface CTASectionProps {
  className?: string;
}

export function CTASection({ className = '' }: CTASectionProps) {
  const benefits = [
    { icon: CheckCircle, text: 'Sin configuración compleja' },
    { icon: Zap, text: 'Resultados inmediatos' },
    { icon: Shield, text: 'Soporte 24/7' },
    { icon: Clock, text: 'Disponible siempre' }
  ];

  const stats = [
    { value: '500+', label: 'Empresas Confían en Emma' },
    { value: '95%', label: 'Tasa de Satisfacción' },
    { value: '24/7', label: 'Soporte Disponible' },
    { value: '10x', label: 'Más Productivo' }
  ];

  return (
    <div className={`relative px-6 py-16 max-w-7xl mx-auto ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl p-12 text-center overflow-hidden"
      >
        {/* Background Decorations */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 right-0 w-40 h-40 bg-white rounded-full translate-x-20 translate-y-20"></div>
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white rounded-full"></div>
          <div className="absolute top-1/4 right-1/3 w-16 h-16 bg-white rounded-full"></div>
        </div>
        
        <div className="relative z-10">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <div className="inline-flex items-center gap-2 mb-4">
              <Sparkles className="w-6 h-6 text-white" />
              <Badge className="bg-white/20 text-white border-white/30 backdrop-blur-md">
                Oferta Especial de Lanzamiento
              </Badge>
            </div>
            <h2 className="text-3xl md:text-5xl font-black text-white mb-6">
              ¿Listo para Transformar tu Negocio?
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Únete a cientos de empresas que ya están usando los agentes de Emma para 
              impulsar su productividad y generar más ingresos.
            </p>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
          >
            <Button 
              size="lg"
              className="bg-white text-[#3018ef] hover:bg-white/90 font-bold px-8 py-4 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            >
              <span className="flex items-center gap-2">
                <Play className="w-5 h-5" />
                Comenzar Ahora - Gratis
              </span>
            </Button>
            <Button 
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-[#3018ef] font-bold px-8 py-4 transition-all duration-300"
            >
              <span className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Agendar Demo Personalizada
              </span>
            </Button>
          </motion.div>

          {/* Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-wrap justify-center items-center gap-6 mb-8 text-white/90"
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8 + index * 0.1 }}
                className="flex items-center gap-2"
              >
                <benefit.icon className="w-5 h-5" />
                <span className="text-sm font-medium">{benefit.text}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2 + index * 0.1 }}
                className="text-center"
              >
                <div className="text-2xl md:text-3xl font-black text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-white/80">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.4 }}
            className="mt-8 pt-8 border-t border-white/20"
          >
            <p className="text-white/80 text-sm mb-4">
              Empresas de todos los tamaños confían en Emma
            </p>
            <div className="flex justify-center items-center gap-8 opacity-60">
              {/* Placeholder for company logos */}
              <div className="w-20 h-8 bg-white/20 rounded-md flex items-center justify-center">
                <span className="text-xs text-white font-semibold">TECH</span>
              </div>
              <div className="w-20 h-8 bg-white/20 rounded-md flex items-center justify-center">
                <span className="text-xs text-white font-semibold">STARTUP</span>
              </div>
              <div className="w-20 h-8 bg-white/20 rounded-md flex items-center justify-center">
                <span className="text-xs text-white font-semibold">CORP</span>
              </div>
              <div className="w-20 h-8 bg-white/20 rounded-md flex items-center justify-center">
                <span className="text-xs text-white font-semibold">AGENCY</span>
              </div>
            </div>
          </motion.div>

          {/* Urgency Element */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.6 }}
            className="mt-6"
          >
            <Badge className="bg-yellow-400 text-yellow-900 border-yellow-500 px-4 py-2 font-semibold">
              🔥 Oferta limitada: 50% de descuento en el primer mes
            </Badge>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
