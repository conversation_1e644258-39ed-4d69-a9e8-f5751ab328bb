import React from 'react';
import { motion } from 'framer-motion';
import { Users, Star, Zap, Award } from 'lucide-react';
import { agentsData } from '@/data/agents-data';

export function MarketplaceStats() {
  const stats = [
    { icon: Users, value: `${agentsData.length}+`, label: 'Agentes Disponibles' },
    { icon: Star, value: '4.8', label: 'Calificación Promedio' },
    { icon: Zap, value: '24/7', label: 'Disponibilidad' },
    { icon: Award, value: '95%', label: 'Tasa de Éxito' }
  ];

  return (
    <div className="flex justify-center gap-8 mb-8">
      {stats.map((stat, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 + index * 0.1 }}
          className="text-center"
        >
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-white/20 backdrop-blur-md mb-2">
            <stat.icon className="w-6 h-6 text-[#3018ef]" />
          </div>
          <div className="text-2xl font-bold text-gray-800">{stat.value}</div>
          <div className="text-sm text-gray-600">{stat.label}</div>
        </motion.div>
      ))}
    </div>
  );
}
