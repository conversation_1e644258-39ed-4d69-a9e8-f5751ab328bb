import React from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  SlidersHorizontal, 
  Grid3X3, 
  List, 
  Star, 
  MessageSquare, 
  User,
  TrendingUp
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface AgentSearchBarProps {
  searchQuery: string;
  onSearch: (query: string) => void;
  sortBy: 'rating' | 'reviews' | 'name';
  onSortChange: (sortBy: 'rating' | 'reviews' | 'name') => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  resultCount: number;
}

export function AgentSearchBar({
  searchQuery,
  onSearch,
  sortBy,
  onSortChange,
  viewMode,
  onViewModeChange,
  resultCount
}: AgentSearchBarProps) {
  const sortOptions = [
    { value: 'rating' as const, label: 'Mejor Calificados', icon: Star },
    { value: 'reviews' as const, label: 'Más Reseñas', icon: MessageSquare },
    { value: 'name' as const, label: 'Nombre A-Z', icon: User }
  ];

  const currentSortOption = sortOptions.find(option => option.value === sortBy);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-8"
    >
      {/* Search and Controls Container */}
      <div className="bg-white/90 backdrop-blur-md rounded-2xl p-6 shadow-xl border border-white/20">
        {/* Top Row - Search and View Controls */}
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          {/* Search Input */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Buscar agentes por nombre, habilidades o especialidad..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
              className="pl-10 pr-4 py-3 text-base border-gray-200 focus:border-[#3018ef] focus:ring-[#3018ef] rounded-xl"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className={`${
                viewMode === 'grid'
                  ? 'bg-[#3018ef] text-white hover:bg-[#3018ef]/90'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className={`${
                viewMode === 'list'
                  ? 'bg-[#3018ef] text-white hover:bg-[#3018ef]/90'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Bottom Row - Results and Sort */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          {/* Results Count and Active Filters */}
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-[#3018ef]/10 text-[#3018ef] border-[#3018ef]/20">
              {resultCount} agente{resultCount !== 1 ? 's' : ''} encontrado{resultCount !== 1 ? 's' : ''}
            </Badge>
            
            {searchQuery && (
              <Badge variant="outline" className="border-gray-300">
                Búsqueda: "{searchQuery}"
              </Badge>
            )}
          </div>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="border-gray-200 hover:bg-gray-50">
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Ordenar por: {currentSortOption?.label}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {sortOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => onSortChange(option.value)}
                  className={`flex items-center gap-2 ${
                    sortBy === option.value ? 'bg-[#3018ef]/10 text-[#3018ef]' : ''
                  }`}
                >
                  <option.icon className="w-4 h-4" />
                  {option.label}
                  {sortBy === option.value && (
                    <TrendingUp className="w-4 h-4 ml-auto" />
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Quick Search Suggestions */}
        {!searchQuery && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ delay: 0.3 }}
            className="mt-4 pt-4 border-t border-gray-100"
          >
            <p className="text-sm text-gray-600 mb-2">Búsquedas populares:</p>
            <div className="flex flex-wrap gap-2">
              {['SEO', 'Contenido', 'Redes Sociales', 'Diseño', 'Email Marketing', 'Análisis'].map((tag) => (
                <Button
                  key={tag}
                  variant="ghost"
                  size="sm"
                  onClick={() => onSearch(tag)}
                  className="text-xs bg-gray-50 hover:bg-[#3018ef]/10 hover:text-[#3018ef] border border-gray-200 rounded-full"
                >
                  {tag}
                </Button>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
