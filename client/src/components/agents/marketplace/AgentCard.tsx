import React from 'react';
import { motion } from 'framer-motion';
import { Agent } from '@/data/agents-data';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { AgentCardHeader } from './AgentCardHeader';
import { AgentCardMetrics } from './AgentCardMetrics';
import { AgentCardActions } from './AgentCardActions';

interface AgentCardProps {
  agent: Agent;
  onSelect: (agent: Agent) => void;
  index: number;
}

export function AgentCard({ agent, onSelect, index }: AgentCardProps) {
  // Animation variants
  const cardVariants = {
    hover: {
      y: -8,
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      whileHover="hover"
      className="group relative"
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full overflow-hidden">
        {/* Header with Avatar and Basic Info */}
        <CardHeader className="pb-4">
          <AgentCardHeader agent={agent} />
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
            {agent.description}
          </p>

          {/* Key Skills */}
          <div>
            <h4 className="text-sm font-semibold text-gray-800 mb-2">Especialidades:</h4>
            <div className="flex flex-wrap gap-1">
              {agent.skills.slice(0, 3).map((skill, skillIndex) => (
                <Badge
                  key={skillIndex}
                  variant="secondary"
                  className="text-xs bg-gray-100 text-gray-700 hover:bg-[#3018ef]/10 hover:text-[#3018ef] transition-colors"
                >
                  {skill.name}
                </Badge>
              ))}
              {agent.skills.length > 3 && (
                <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-500">
                  +{agent.skills.length - 3} más
                </Badge>
              )}
            </div>
          </div>

          {/* Performance Metrics */}
          <AgentCardMetrics agent={agent} />

          {/* Action Buttons */}
          <AgentCardActions agent={agent} onSelect={onSelect} />
        </CardContent>

        {/* Hover Effect Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg" />
      </Card>
    </motion.div>
  );
}
