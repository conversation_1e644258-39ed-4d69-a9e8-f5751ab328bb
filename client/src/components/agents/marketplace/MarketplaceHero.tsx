import React from 'react';
import { motion } from 'framer-motion';
import { Bot } from 'lucide-react';
import { MarketplaceStats } from './MarketplaceStats';

export function MarketplaceHero() {
  return (
    <div className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/10 via-transparent to-[#dd3a5a]/10"></div>
      <div className="relative px-6 py-12 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] mb-6 shadow-lg">
            <Bot className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-6xl font-black bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-4">
            Marketplace de Agentes IA
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Contrata agentes especializados de IA que transformarán tu negocio. 
            Cada agente es un experto en su campo, listo para trabajar 24/7.
          </p>
          
          {/* Quick Stats */}
          <MarketplaceStats />
        </motion.div>
      </div>
    </div>
  );
}
