import React from 'react';
import { motion } from 'framer-motion';
import { Agent, categoriesData } from '@/data/agents-data';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Star, 
  Award, 
  TrendingUp, 
  Zap,
  ArrowRight,
  Crown,
  CheckCircle
} from 'lucide-react';

interface FeaturedAgentsSectionProps {
  agents: Agent[];
  onAgentSelect: (agent: Agent) => void;
}

export function FeaturedAgentsSection({ agents, onAgentSelect }: FeaturedAgentsSectionProps) {
  if (agents.length === 0) return null;

  return (
    <div className="px-6 py-12 max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-12"
      >
        <div className="inline-flex items-center gap-2 mb-4">
          <Crown className="w-6 h-6 text-[#dd3a5a]" />
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            Agentes Destacados
          </h2>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Los agentes más populares y mejor calificados, listos para transformar tu negocio
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {agents.map((agent, index) => (
          <FeaturedAgentCard
            key={agent.id}
            agent={agent}
            onSelect={onAgentSelect}
            index={index}
          />
        ))}
      </div>
    </div>
  );
}

interface FeaturedAgentCardProps {
  agent: Agent;
  onSelect: (agent: Agent) => void;
  index: number;
}

function FeaturedAgentCard({ agent, onSelect, index }: FeaturedAgentCardProps) {
  const category = categoriesData.find(cat => cat.id === agent.categoryId);
  const categoryColor = category?.color || '#3018ef';

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      whileHover={{ y: -8, scale: 1.02 }}
      className="group relative"
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full overflow-hidden">
        {/* Featured Badge */}
        <div className="absolute top-4 right-4 z-10">
          <Badge className="bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] text-white border-0 shadow-lg">
            <Award className="w-3 h-3 mr-1" />
            Destacado
          </Badge>
        </div>

        <CardContent className="p-6">
          {/* Agent Header */}
          <div className="flex items-center gap-4 mb-4">
            <div className="relative">
              <div 
                className="w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold text-white shadow-lg"
                style={{ backgroundColor: categoryColor }}
              >
                {agent.avatar.startsWith('http') ? (
                  <img 
                    src={agent.avatar} 
                    alt={agent.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span>{agent.avatar}</span>
                )}
              </div>
              
              {/* Online Status */}
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <CheckCircle className="w-3 h-3 text-white" />
              </div>
            </div>

            <div className="flex-1">
              <h3 className="font-bold text-lg text-gray-800 mb-1 group-hover:bg-gradient-to-r group-hover:from-[#3018ef] group-hover:to-[#dd3a5a] group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                {agent.name}
              </h3>
              <p className="text-sm text-gray-600">{agent.category}</p>
            </div>
          </div>

          {/* Rating and Stats */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="font-semibold text-gray-700">{agent.rating}</span>
              <span className="text-gray-500 text-sm">({agent.reviewsCount})</span>
            </div>
            
            {agent.performance && (
              <div className="flex items-center gap-2 text-sm">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-green-600 font-semibold">
                  {agent.performance.summary.tasksCompleted} tareas
                </span>
              </div>
            )}
          </div>

          {/* Description */}
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {agent.description}
          </p>

          {/* Key Skills */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {agent.skills.slice(0, 3).map((skill, skillIndex) => (
                <Badge 
                  key={skillIndex}
                  variant="secondary" 
                  className="text-xs bg-gray-100 text-gray-700"
                >
                  {skill.name}
                </Badge>
              ))}
            </div>
          </div>

          {/* Performance Highlight */}
          {agent.performance && (
            <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-[#3018ef]" />
                  <span className="font-semibold">Velocidad:</span>
                </div>
                <span className="text-[#3018ef] font-bold">
                  {agent.performance.summary.executionSpeed}
                </span>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelect(agent)}
              className="flex-1 border-gray-200 hover:bg-gray-50"
            >
              Ver Perfil
            </Button>
            <Button
              size="sm"
              onClick={() => {
                if (agent.id === "seox-analyzer") {
                  window.location.href = "/seox-analyzer";
                } else if (agent.id === "instagram-copywriter") {
                  window.location.href = "/instagram-copywriter";
                } else {
                  onSelect(agent);
                }
              }}
              className="flex-1 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white hover:shadow-lg transform hover:scale-105 transition-all duration-300"
            >
              <span className="flex items-center gap-1">
                Contratar
                <ArrowRight className="w-3 h-3" />
              </span>
            </Button>
          </div>
        </CardContent>

        {/* Hover Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg" />
      </Card>
    </motion.div>
  );
}
