import React from 'react';
import { Agent } from '@/data/agents-data';

interface AgentCardMetricsProps {
  agent: Agent;
}

export function AgentCardMetrics({ agent }: AgentCardMetricsProps) {
  if (!agent.performance) {
    return null;
  }

  return (
    <div className="grid grid-cols-2 gap-3 py-3 border-t border-gray-100">
      <div className="text-center">
        <div className="text-lg font-bold text-[#3018ef]">
          {agent.performance.summary.tasksCompleted}
        </div>
        <div className="text-xs text-gray-500"><PERSON><PERSON>s <PERSON>tad<PERSON></div>
      </div>
      <div className="text-center">
        <div className="text-lg font-bold text-[#dd3a5a]">
          {agent.performance.summary.satisfactionLevel}
        </div>
        <div className="text-xs text-gray-500">Satisfacción</div>
      </div>
    </div>
  );
}
