import React from 'react';
import { motion } from 'framer-motion';
import { Agent, categoriesData } from '@/data/agents-data';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Star, 
  MessageSquare, 
  Eye, 
  ArrowRight,
  CheckCircle,
  Clock,
  TrendingUp,
  Award
} from 'lucide-react';

interface AgentListItemProps {
  agent: Agent;
  onSelect: (agent: Agent) => void;
  index: number;
}

export function AgentListItem({ agent, onSelect, index }: AgentListItemProps) {
  // Get category info for styling
  const category = categoriesData.find(cat => cat.id === agent.categoryId);
  const categoryColor = category?.color || '#3018ef';

  // Get level color
  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Básico': return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermedio': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Avanzado': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Experto': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.01, y: -2 }}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
      className="group"
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center gap-6">
            {/* Agent Avatar and Status */}
            <div className="relative flex-shrink-0">
              <div 
                className="w-20 h-20 rounded-full flex items-center justify-center text-2xl font-bold text-white shadow-lg"
                style={{ backgroundColor: categoryColor }}
              >
                {agent.avatar.startsWith('http') ? (
                  <img 
                    src={agent.avatar} 
                    alt={agent.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span>{agent.avatar}</span>
                )}
              </div>
              
              {/* Online Status */}
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-1 group-hover:bg-gradient-to-r group-hover:from-[#3018ef] group-hover:to-[#dd3a5a] group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
                    {agent.name}
                  </h3>
                  <p className="text-gray-600 mb-2">{agent.category}</p>
                  
                  {/* Rating and Reviews */}
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-semibold text-gray-700">{agent.rating}</span>
                      <span className="text-gray-500">({agent.reviewsCount} reseñas)</span>
                    </div>
                    <Badge className={`${getLevelColor(agent.level)} border`}>
                      {agent.level}
                    </Badge>
                  </div>
                </div>

                {/* Performance Metrics */}
                {agent.performance && (
                  <div className="flex gap-6 text-center">
                    <div>
                      <div className="text-lg font-bold text-[#3018ef]">
                        {agent.performance.summary.tasksCompleted}
                      </div>
                      <div className="text-xs text-gray-500">Tareas</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-[#dd3a5a]">
                        {agent.performance.summary.satisfactionLevel}
                      </div>
                      <div className="text-xs text-gray-500">Satisfacción</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">
                        {agent.performance.summary.executionSpeed}
                      </div>
                      <div className="text-xs text-gray-500">Velocidad</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Description */}
              <p className="text-gray-600 mb-4 line-clamp-2">
                {agent.description}
              </p>

              {/* Skills and Benefits */}
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {agent.skills.slice(0, 4).map((skill, skillIndex) => (
                      <Badge 
                        key={skillIndex}
                        variant="secondary" 
                        className="text-xs bg-gray-100 text-gray-700 hover:bg-[#3018ef]/10 hover:text-[#3018ef] transition-colors"
                      >
                        {skill.name}
                      </Badge>
                    ))}
                    {agent.skills.length > 4 && (
                      <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-500">
                        +{agent.skills.length - 4} más
                      </Badge>
                    )}
                  </div>

                  {/* Key Benefits */}
                  <div className="text-sm text-gray-600">
                    <strong>Beneficios clave:</strong> {agent.keyBenefits.slice(0, 2).join(', ')}
                    {agent.keyBenefits.length > 2 && '...'}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 ml-6">
                  <Button
                    variant="outline"
                    onClick={() => onSelect(agent)}
                    className="border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Ver Perfil
                  </Button>
                  <Button
                    onClick={() => {
                      // Handle direct hiring
                      if (agent.id === "seox-analyzer") {
                        window.location.href = "/seox-analyzer";
                      } else if (agent.id === "instagram-copywriter") {
                        window.location.href = "/instagram-copywriter";
                      } else {
                        onSelect(agent);
                      }
                    }}
                    className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                  >
                    <span className="flex items-center gap-2">
                      Contratar Ahora
                      <ArrowRight className="w-4 h-4" />
                    </span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>

        {/* Hover Effect Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-lg" />
      </Card>
    </motion.div>
  );
}
