import React from 'react';
import { motion } from 'framer-motion';
import { Agent } from '@/data/agents-data';
import { AgentCard } from './AgentCard';
import { AgentListItem } from './AgentListItem';
import { Card, CardContent } from '@/components/ui/card';
import { Bot, Search } from 'lucide-react';

interface AgentGridProps {
  agents: Agent[];
  viewMode: 'grid' | 'list';
  onAgentSelect: (agent: Agent) => void;
}

export function AgentGrid({ agents, viewMode, onAgentSelect }: AgentGridProps) {
  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Empty state
  if (agents.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center py-16"
      >
        <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl max-w-md mx-auto">
          <CardContent className="p-8">
            <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">
              No se encontraron agentes
            </h3>
            <p className="text-gray-600 mb-6">
              Intenta ajustar tus filtros de búsqueda o explora diferentes categorías.
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Bot className="w-4 h-4" />
              <span>Tenemos {agents.length} agentes increíbles esperándote</span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={`${
        viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
          : 'space-y-4'
      }`}
    >
      {agents.map((agent, index) => (
        <motion.div
          key={agent.id}
          variants={itemVariants}
          transition={{ duration: 0.5, delay: index * 0.05 }}
        >
          {viewMode === 'grid' ? (
            <AgentCard
              agent={agent}
              onSelect={onAgentSelect}
              index={index}
            />
          ) : (
            <AgentListItem
              agent={agent}
              onSelect={onAgentSelect}
              index={index}
            />
          )}
        </motion.div>
      ))}
    </motion.div>
  );
}
