import React from 'react';
import { Agent, categoriesData } from '@/data/agents-data';
import { Badge } from '@/components/ui/badge';
import { Star, MessageSquare, CheckCircle } from 'lucide-react';

interface AgentCardHeaderProps {
  agent: Agent;
}

export function AgentCardHeader({ agent }: AgentCardHeaderProps) {
  const category = categoriesData.find(cat => cat.id === agent.categoryId);
  const categoryColor = category?.color || '#3018ef';

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Básico': return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermedio': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Avanzado': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Experto': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="flex items-start gap-4">
      {/* Agent Avatar */}
      <div className="relative">
        <div 
          className="w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold text-white shadow-lg"
          style={{ backgroundColor: categoryColor }}
        >
          {agent.avatar.startsWith('http') ? (
            <img 
              src={agent.avatar} 
              alt={agent.name}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span>{agent.avatar}</span>
          )}
        </div>
        
        {/* Online Status Indicator */}
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
          <CheckCircle className="w-3 h-3 text-white" />
        </div>
      </div>

      {/* Agent Info */}
      <div className="flex-1 min-w-0">
        <h3 className="font-bold text-lg text-gray-800 mb-1 group-hover:bg-gradient-to-r group-hover:from-[#3018ef] group-hover:to-[#dd3a5a] group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300">
          {agent.name}
        </h3>
        <p className="text-sm text-gray-600 mb-2">{agent.category}</p>
        
        {/* Rating and Reviews */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="text-sm font-semibold text-gray-700">{agent.rating}</span>
          </div>
          <div className="flex items-center gap-1 text-gray-500">
            <MessageSquare className="w-4 h-4" />
            <span className="text-sm">{agent.reviewsCount}</span>
          </div>
        </div>
      </div>

      {/* Level Badge */}
      <Badge className={`${getLevelColor(agent.level)} border`}>
        {agent.level}
      </Badge>
    </div>
  );
}
