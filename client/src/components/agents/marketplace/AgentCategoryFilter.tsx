import React from 'react';
import { motion } from 'framer-motion';
import { AgentCategory } from '@/data/agents-data';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Filter, X, Sparkles } from 'lucide-react';

interface AgentCategoryFilterProps {
  categories: AgentCategory[];
  selectedCategory: string | null;
  onCategorySelect: (categoryId: string | null) => void;
  agentCounts: Record<string, number>;
}

export function AgentCategoryFilter({
  categories,
  selectedCategory,
  onCategorySelect,
  agentCounts
}: AgentCategoryFilterProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl sticky top-6">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            <Filter className="w-5 h-5 text-[#3018ef]" />
            Categorías
          </CardTitle>
          {selectedCategory && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onCategorySelect(null)}
              className="w-fit text-gray-600 hover:text-gray-800"
            >
              <X className="w-4 h-4 mr-1" />
              Limpiar filtros
            </Button>
          )}
        </CardHeader>
        
        <CardContent className="space-y-2">
          {/* All Categories Option */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant={selectedCategory === null ? "default" : "ghost"}
              className={`w-full justify-between text-left h-auto p-4 ${
                selectedCategory === null
                  ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => onCategorySelect(null)}
            >
              <div className="flex items-center gap-3">
                <div className="text-2xl">🤖</div>
                <div>
                  <div className="font-semibold">Todos los Agentes</div>
                  <div className={`text-sm ${selectedCategory === null ? 'text-white/80' : 'text-gray-500'}`}>
                    Ver toda la colección
                  </div>
                </div>
              </div>
              <Badge 
                variant="secondary" 
                className={`${
                  selectedCategory === null 
                    ? 'bg-white/20 text-white border-white/30' 
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {Object.values(agentCounts).reduce((sum, count) => sum + count, 0)}
              </Badge>
            </Button>
          </motion.div>

          {/* Category Options */}
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant={selectedCategory === category.id ? "default" : "ghost"}
                className={`w-full justify-between text-left h-auto p-4 ${
                  selectedCategory === category.id
                    ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => onCategorySelect(category.id)}
              >
                <div className="flex items-center gap-3">
                  <div className="text-2xl">{category.icon}</div>
                  <div>
                    <div className="font-semibold">{category.name}</div>
                    <div className={`text-sm ${selectedCategory === category.id ? 'text-white/80' : 'text-gray-500'}`}>
                      {category.description}
                    </div>
                  </div>
                </div>
                <Badge 
                  variant="secondary" 
                  className={`${
                    selectedCategory === category.id 
                      ? 'bg-white/20 text-white border-white/30' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {agentCounts[category.id] || 0}
                </Badge>
              </Button>
            </motion.div>
          ))}
        </CardContent>

        {/* Premium Features Teaser */}
        <CardContent className="pt-0">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="mt-6 p-4 rounded-xl bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 border border-[#3018ef]/20"
          >
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-4 h-4 text-[#3018ef]" />
              <span className="text-sm font-semibold text-gray-800">Próximamente</span>
            </div>
            <p className="text-xs text-gray-600 mb-3">
              Agentes personalizados, integración con tu CRM y análisis avanzado de rendimiento.
            </p>
            <Button 
              size="sm" 
              className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white hover:shadow-lg transition-all"
            >
              Únete a la Lista de Espera
            </Button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
