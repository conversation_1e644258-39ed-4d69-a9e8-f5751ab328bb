import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Agent, categoriesData } from '@/data/agents-data';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  X, 
  Star, 
  MessageSquare, 
  CheckCircle, 
  Award,
  TrendingUp,
  Clock,
  Zap,
  Users,
  ArrowRight,
  Play,
  Download
} from 'lucide-react';

interface AgentProfileModalProps {
  agent: Agent;
  onClose: () => void;
}

export function AgentProfileModal({ agent, onClose }: AgentProfileModalProps) {
  const [activeTab, setActiveTab] = useState('overview');
  
  const category = categoriesData.find(cat => cat.id === agent.categoryId);
  const categoryColor = category?.color || '#3018ef';

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Básico': return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermedio': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Avanzado': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Experto': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleHire = () => {
    if (agent.id === "seox-analyzer") {
      window.location.href = "/seox-analyzer";
    } else if (agent.id === "instagram-copywriter") {
      window.location.href = "/instagram-copywriter";
    } else {
      // For other agents, could open hiring flow
      console.log('Hiring agent:', agent.name);
      onClose();
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] p-6 text-white">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="absolute top-4 right-4 text-white hover:bg-white/20"
            >
              <X className="w-5 h-5" />
            </Button>

            <div className="flex items-start gap-6">
              {/* Agent Avatar */}
              <div className="relative">
                <div 
                  className="w-24 h-24 rounded-full flex items-center justify-center text-3xl font-bold text-white shadow-lg border-4 border-white/20"
                  style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
                >
                  {agent.avatar.startsWith('http') ? (
                    <img 
                      src={agent.avatar} 
                      alt={agent.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <span>{agent.avatar}</span>
                  )}
                </div>
                
                {/* Online Status */}
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              </div>

              {/* Agent Info */}
              <div className="flex-1">
                <h1 className="text-3xl font-bold mb-2">{agent.name}</h1>
                <p className="text-white/90 mb-3">{agent.category}</p>
                
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center gap-1">
                    <Star className="w-5 h-5 text-yellow-300 fill-current" />
                    <span className="font-semibold">{agent.rating}</span>
                    <span className="text-white/80">({agent.reviewsCount} reseñas)</span>
                  </div>
                  <Badge className={`${getLevelColor(agent.level)} border`}>
                    {agent.level}
                  </Badge>
                </div>

                {/* Quick Stats */}
                {agent.performance && (
                  <div className="flex gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{agent.performance.summary.tasksCompleted}</div>
                      <div className="text-sm text-white/80">Tareas Completadas</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{agent.performance.summary.satisfactionLevel}</div>
                      <div className="text-sm text-white/80">Satisfacción</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">{agent.performance.summary.executionSpeed}</div>
                      <div className="text-sm text-white/80">Velocidad</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Hire Button */}
              <div className="text-right">
                <Button
                  onClick={handleHire}
                  className="bg-white text-[#3018ef] hover:bg-white/90 font-bold px-8 py-3 shadow-lg"
                >
                  <span className="flex items-center gap-2">
                    Contratar Ahora
                    <ArrowRight className="w-5 h-5" />
                  </span>
                </Button>
                <p className="text-sm text-white/80 mt-2">Disponible 24/7</p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Resumen</TabsTrigger>
                <TabsTrigger value="skills">Habilidades</TabsTrigger>
                <TabsTrigger value="performance">Rendimiento</TabsTrigger>
                <TabsTrigger value="examples">Ejemplos</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* About */}
                <Card>
                  <CardHeader>
                    <CardTitle>Acerca de {agent.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 leading-relaxed">{agent.about}</p>
                  </CardContent>
                </Card>

                {/* Key Benefits */}
                <Card>
                  <CardHeader>
                    <CardTitle>Beneficios Clave</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {agent.keyBenefits.map((benefit, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* How It Works */}
                <Card>
                  <CardHeader>
                    <CardTitle>Cómo Funciona</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {agent.howItWorksSteps.map((step, index) => (
                        <div key={index} className="flex gap-4">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white flex items-center justify-center font-bold text-sm">
                            {index + 1}
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-800 mb-1">{step.title}</h4>
                            <p className="text-gray-600">{step.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="skills" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Habilidades y Especialidades</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {agent.skills.map((skill, index) => (
                        <div key={index}>
                          <div className="flex justify-between mb-2">
                            <span className="font-medium text-gray-800">{skill.name}</span>
                            <span className="text-sm text-gray-600">{skill.level}/10</span>
                          </div>
                          <Progress value={skill.level * 10} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="performance" className="space-y-6">
                {agent.performance && (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle>Métricas de Rendimiento</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-[#3018ef]">
                              {agent.performance.summary.timeFreed}
                            </div>
                            <div className="text-sm text-gray-600">Tiempo Liberado</div>
                          </div>
                          <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-[#dd3a5a]">
                              {agent.performance.summary.tasksCompleted}
                            </div>
                            <div className="text-sm text-gray-600">Tareas Completadas</div>
                          </div>
                          <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">
                              {agent.performance.summary.executionSpeed}
                            </div>
                            <div className="text-sm text-gray-600">Velocidad</div>
                          </div>
                          <div className="text-center p-4 bg-gray-50 rounded-lg">
                            <div className="text-2xl font-bold text-yellow-600">
                              {agent.performance.summary.satisfactionLevel}
                            </div>
                            <div className="text-sm text-gray-600">Satisfacción</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}
              </TabsContent>

              <TabsContent value="examples" className="space-y-6">
                {agent.examples && agent.examples.length > 0 ? (
                  <div className="space-y-4">
                    {agent.examples.map((example, index) => (
                      <Card key={index}>
                        <CardHeader>
                          <CardTitle className="text-lg">{example.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-600">{example.content}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="text-center py-8">
                      <p className="text-gray-500">Ejemplos de trabajo próximamente disponibles</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
