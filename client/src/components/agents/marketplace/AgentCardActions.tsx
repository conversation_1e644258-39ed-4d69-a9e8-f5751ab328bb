import React from 'react';
import { Agent } from '@/data/agents-data';
import { Button } from '@/components/ui/button';
import { Eye, ArrowRight } from 'lucide-react';
import { useAgentActions } from '../hooks/useAgentActions';

interface AgentCardActionsProps {
  agent: Agent;
  onSelect: (agent: Agent) => void;
}

export function AgentCardActions({ agent, onSelect }: AgentCardActionsProps) {
  const { handleHireAgent } = useAgentActions();

  return (
    <div className="flex gap-2 pt-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onSelect(agent)}
        className="flex-1 border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-all"
      >
        <Eye className="w-4 h-4 mr-2" />
        Ver Perfil
      </Button>
      <Button
        size="sm"
        onClick={() => handleHireAgent(agent, onSelect)}
        className="flex-1 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white hover:shadow-lg transform hover:scale-105 transition-all duration-300"
      >
        <span className="flex items-center gap-2">
          Contratar
          <ArrowRight className="w-4 h-4" />
        </span>
      </Button>
    </div>
  );
}
