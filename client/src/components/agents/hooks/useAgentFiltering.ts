import { useState, useMemo } from 'react';
import { agentsData, categoriesData, Agent } from '@/data/agents-data';

export function useAgentFiltering() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'rating' | 'reviews' | 'name'>('rating');

  // Filter and sort agents based on current state
  const filteredAgents = useMemo(() => {
    let filtered = agentsData;

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(agent => agent.categoryId === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(agent => 
        agent.name.toLowerCase().includes(query) ||
        agent.description.toLowerCase().includes(query) ||
        agent.skills.some(skill => skill.name.toLowerCase().includes(query)) ||
        agent.keyBenefits.some(benefit => benefit.toLowerCase().includes(query))
      );
    }

    // Sort agents
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'reviews':
          return b.reviewsCount - a.reviewsCount;
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    return filtered;
  }, [selectedCategory, searchQuery, sortBy]);

  // Get featured agents (top rated agents from different categories)
  const featuredAgents = useMemo(() => {
    const categoryAgents = categoriesData.map(category => {
      const categoryAgents = agentsData.filter(agent => agent.categoryId === category.id);
      return categoryAgents.sort((a, b) => b.rating - a.rating)[0];
    }).filter(Boolean);
    
    return categoryAgents.slice(0, 6);
  }, []);

  // Calculate agent counts per category
  const agentCounts = useMemo(() => {
    return categoriesData.reduce((acc, cat) => {
      acc[cat.id] = agentsData.filter(agent => agent.categoryId === cat.id).length;
      return acc;
    }, {} as Record<string, number>);
  }, []);

  const handleCategorySelect = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleSortChange = (newSortBy: 'rating' | 'reviews' | 'name') => {
    setSortBy(newSortBy);
  };

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  return {
    selectedCategory,
    searchQuery,
    viewMode,
    sortBy,
    filteredAgents,
    featuredAgents,
    agentCounts,
    handleCategorySelect,
    handleSearch,
    handleSortChange,
    handleViewModeChange
  };
}
