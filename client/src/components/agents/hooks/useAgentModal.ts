import { useState } from 'react';
import { Agent } from '@/data/agents-data';

export function useAgentModal() {
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);

  const handleAgentSelect = (agent: Agent) => {
    setSelectedAgent(agent);
  };

  const handleCloseProfile = () => {
    setSelectedAgent(null);
  };

  return {
    selectedAgent,
    handleAgentSelect,
    handleCloseProfile
  };
}
