import { Agent } from '@/data/agents-data';

export function useAgentActions() {
  const handleHireAgent = (agent: Agent, onSelect: (agent: Agent) => void) => {
    // Handle direct hiring - could navigate to specific agent page
    if (agent.id === "seox-analyzer") {
      window.location.href = "/seox-analyzer";
    } else if (agent.id === "instagram-copywriter") {
      window.location.href = "/instagram-copywriter";
    } else {
      onSelect(agent);
    }
  };

  return {
    handleHireAgent
  };
}
